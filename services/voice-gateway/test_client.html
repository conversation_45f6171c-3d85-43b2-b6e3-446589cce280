<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Gateway Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .status.error { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border-radius: 4px;
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .audio-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 12px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>Voice Gateway Test Client</h1>
    
    <div class="container">
        <h3>Connection Settings</h3>
        <div class="controls">
            <label>Server URL:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8002" placeholder="ws://localhost:8002">
            
            <label>Call ID:</label>
            <input type="text" id="callId" value="test-call-123" placeholder="test-call-123">
            
            <label>JWT Token:</label>
            <input type="text" id="jwtToken" value="your-jwt-token-here" placeholder="JWT Token">
        </div>
        
        <div class="controls">
            <button id="connectBtn" onclick="connectWebSocket()">Connect</button>
            <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled>Disconnect</button>
            <button onclick="sendPing()">Send Ping</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div id="connectionStatus" class="status disconnected">Disconnected</div>
    </div>

    <div class="container">
        <h3>Audio Recording</h3>
        <div class="controls">
            <button id="recordBtn" onclick="startRecording()" disabled>Start Recording</button>
            <button id="stopBtn" onclick="stopRecording()" disabled>Stop Recording</button>
            <button onclick="testAudioPermissions()">Test Microphone</button>
        </div>
        
        <div id="audioInfo" class="audio-info">
            <div>Microphone: Not initialized</div>
            <div>Audio Context: Not created</div>
            <div>Sample Rate: Unknown</div>
            <div>Buffer Size: Unknown</div>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="bytesSent">0</div>
                <div class="stat-label">Bytes Sent</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="bytesReceived">0</div>
                <div class="stat-label">Bytes Received</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="audioChunks">0</div>
                <div class="stat-label">Audio Chunks</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="recordingTime">0.0s</div>
                <div class="stat-label">Recording Time</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>Audio Playback</h3>
        <div class="controls">
            <button onclick="togglePlayback()">Enable/Disable Playback</button>
            <input type="range" id="volumeSlider" min="0" max="100" value="50" onchange="setVolume(this.value)">
            <label>Volume: <span id="volumeLabel">50%</span></label>
        </div>
        <audio id="audioPlayer" controls style="width: 100%; margin: 10px 0;"></audio>
    </div>

    <div class="container">
        <h3>Debug Log</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let websocket = null;
        let audioContext = null;
        let mediaStreamSource = null;
        let scriptProcessor = null;
        let mediaStream = null;
        let isRecording = false;
        let playbackEnabled = true;
        let recordingStartTime = 0;
        
        // Statistics
        let stats = {
            bytesSent: 0,
            bytesReceived: 0,
            audioChunks: 0
        };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const color = {
                'info': '#00ff00',
                'error': '#ff4444',
                'warning': '#ffaa00',
                'success': '#44ff44'
            }[type] || '#00ff00';
            
            logElement.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }

        function updateStats() {
            document.getElementById('bytesSent').textContent = stats.bytesSent.toLocaleString();
            document.getElementById('bytesReceived').textContent = stats.bytesReceived.toLocaleString();
            document.getElementById('audioChunks').textContent = stats.audioChunks.toLocaleString();
            
            if (isRecording && recordingStartTime > 0) {
                const duration = (Date.now() - recordingStartTime) / 1000;
                document.getElementById('recordingTime').textContent = duration.toFixed(1) + 's';
            }
        }

        function connectWebSocket() {
            const serverUrl = document.getElementById('serverUrl').value;
            const callId = document.getElementById('callId').value;
            const jwtToken = document.getElementById('jwtToken').value;
            
            if (!serverUrl || !callId || !jwtToken) {
                log('Please fill in all connection fields', 'error');
                return;
            }
            
            const wsUrl = `${serverUrl}/api/v1/call/ws/call/${callId}?token=${jwtToken}`;
            log(`Connecting to: ${wsUrl}`, 'info');
            
            try {
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = function() {
                    log('WebSocket connected successfully', 'success');
                    updateConnectionStatus('connected', 'Connected');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('recordBtn').disabled = false;
                };
                
                websocket.onmessage = function(event) {
                    if (event.data instanceof Blob) {
                        // Received audio data
                        stats.bytesReceived += event.data.size;
                        log(`Received audio: ${event.data.size} bytes`, 'info');
                        
                        if (playbackEnabled) {
                            playAudio(event.data);
                        }
                    } else {
                        // Received text message
                        try {
                            const message = JSON.parse(event.data);
                            log(`Control message: ${JSON.stringify(message)}`, 'info');
                        } catch (e) {
                            log(`Text message: ${event.data}`, 'info');
                        }
                    }
                    updateStats();
                };
                
                websocket.onclose = function(event) {
                    log(`WebSocket closed: ${event.code} - ${event.reason}`, 'warning');
                    updateConnectionStatus('disconnected', 'Disconnected');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('recordBtn').disabled = true;
                    stopRecording();
                };
                
                websocket.onerror = function(error) {
                    log(`WebSocket error: ${error}`, 'error');
                    updateConnectionStatus('error', 'Connection Error');
                };
                
            } catch (error) {
                log(`Failed to create WebSocket: ${error.message}`, 'error');
            }
        }

        function disconnectWebSocket() {
            if (websocket) {
                stopRecording();
                websocket.close();
                websocket = null;
            }
        }

        function sendPing() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const pingMessage = JSON.stringify({type: "ping"});
                websocket.send(pingMessage);
                log('Sent ping message', 'info');
            } else {
                log('WebSocket not connected', 'error');
            }
        }

        async function testAudioPermissions() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: false
                    }
                });

                log('Microphone access granted', 'success');
                updateAudioInfo('Microphone: Access granted', null, null, null);

                // Stop the test stream
                stream.getTracks().forEach(track => track.stop());

            } catch (error) {
                log(`Microphone access denied: ${error.message}`, 'error');
                updateAudioInfo('Microphone: Access denied', null, null, null);
            }
        }

        function updateAudioInfo(mic, context, sampleRate, bufferSize) {
            const infoElement = document.getElementById('audioInfo');
            const lines = infoElement.innerHTML.split('<div>').slice(1).map(line => line.replace('</div>', ''));

            if (mic !== null) lines[0] = mic;
            if (context !== null) lines[1] = `Audio Context: ${context}`;
            if (sampleRate !== null) lines[2] = `Sample Rate: ${sampleRate}Hz`;
            if (bufferSize !== null) lines[3] = `Buffer Size: ${bufferSize} samples`;

            infoElement.innerHTML = lines.map(line => `<div>${line}</div>`).join('');
        }

        async function startRecording() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                log('Connect WebSocket before recording', 'error');
                return;
            }

            try {
                // Get user media
                mediaStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: false
                    }
                });

                // Create AudioContext without forcing sample rate
                audioContext = new (window.AudioContext || window.webkitAudioContext)();

                log(`AudioContext created with sample rate: ${audioContext.sampleRate}Hz`, 'info');
                updateAudioInfo(null, 'Created', audioContext.sampleRate, null);

                mediaStreamSource = audioContext.createMediaStreamSource(mediaStream);

                // Use smaller buffer for lower latency
                const bufferSize = 1024;
                scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);
                updateAudioInfo(null, null, null, bufferSize);

                scriptProcessor.onaudioprocess = (event) => {
                    if (!websocket || websocket.readyState !== WebSocket.OPEN || !isRecording) {
                        return;
                    }

                    const inputData = event.inputBuffer.getChannelData(0);
                    const actualSampleRate = audioContext.sampleRate;

                    // Resample to 16kHz if needed
                    let processedData = inputData;
                    if (actualSampleRate !== 16000) {
                        processedData = resampleAudio(inputData, actualSampleRate, 16000);
                    }

                    // Convert Float32 to 16-bit PCM
                    const buffer = new ArrayBuffer(processedData.length * 2);
                    const view = new DataView(buffer);
                    for (let i = 0; i < processedData.length; i++) {
                        const sample = Math.max(-1, Math.min(1, processedData[i]));
                        const intSample = Math.round(sample * 32767);
                        view.setInt16(i * 2, intSample, true); // little-endian
                    }

                    websocket.send(buffer);
                    stats.bytesSent += buffer.byteLength;
                    stats.audioChunks++;
                };

                mediaStreamSource.connect(scriptProcessor);
                scriptProcessor.connect(audioContext.destination);

                isRecording = true;
                recordingStartTime = Date.now();
                document.getElementById('recordBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                log('Started real-time audio streaming', 'success');

            } catch (error) {
                log(`Recording error: ${error.message}`, 'error');
                updateAudioInfo('Microphone: Error', null, null, null);
            }
        }

        function stopRecording() {
            if (scriptProcessor) {
                scriptProcessor.disconnect();
                scriptProcessor = null;
            }
            if (mediaStreamSource) {
                mediaStreamSource.disconnect();
                mediaStreamSource = null;
            }
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
            }

            isRecording = false;
            recordingStartTime = 0;
            document.getElementById('recordBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            updateAudioInfo('Microphone: Stopped', 'Closed', 'Unknown', 'Unknown');
            log('Stopped audio streaming', 'info');
        }

        // Simple linear resampling function
        function resampleAudio(inputBuffer, inputSampleRate, outputSampleRate) {
            if (inputSampleRate === outputSampleRate) {
                return inputBuffer;
            }

            const ratio = inputSampleRate / outputSampleRate;
            const outputLength = Math.round(inputBuffer.length / ratio);
            const outputBuffer = new Float32Array(outputLength);

            for (let i = 0; i < outputLength; i++) {
                const inputIndex = i * ratio;
                const inputIndexFloor = Math.floor(inputIndex);
                const inputIndexCeil = Math.min(inputIndexFloor + 1, inputBuffer.length - 1);
                const fraction = inputIndex - inputIndexFloor;

                // Linear interpolation
                outputBuffer[i] = inputBuffer[inputIndexFloor] * (1 - fraction) +
                                 inputBuffer[inputIndexCeil] * fraction;
            }

            return outputBuffer;
        }

        function playAudio(audioBlob) {
            try {
                const audioUrl = URL.createObjectURL(audioBlob);
                const audioPlayer = document.getElementById('audioPlayer');
                audioPlayer.src = audioUrl;
                audioPlayer.play().catch(e => {
                    log(`Audio playback error: ${e.message}`, 'warning');
                });

                // Clean up URL after playing
                audioPlayer.onended = () => {
                    URL.revokeObjectURL(audioUrl);
                };
            } catch (error) {
                log(`Audio playback setup error: ${error.message}`, 'error');
            }
        }

        function togglePlayback() {
            playbackEnabled = !playbackEnabled;
            log(`Audio playback ${playbackEnabled ? 'enabled' : 'disabled'}`, 'info');
        }

        function setVolume(value) {
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.volume = value / 100;
            document.getElementById('volumeLabel').textContent = value + '%';
        }

        // Update stats every second
        setInterval(updateStats, 1000);

        // Initialize volume
        setVolume(50);
    </script>
</body>
</html>
