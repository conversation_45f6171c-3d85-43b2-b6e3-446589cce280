"""
Provider factory for creating voice processing providers.

This module implements the factory pattern to create appropriate providers
based on configuration settings.
"""

import logging
from typing import Dict, Type, Any

from .base import BaseST<PERSON>rovider, BaseTTSProvider, BaseTranslationProvider, BaseVADProvider
from .openai_tts import OpenAITTSProvider
from .deepgram_tts import DeepGramTTSProvider
from .whisper_stt import WhisperSTTProvider
from .deepgram_stt import DeepGramSTTProvider
from .huggingface_translation import HuggingFaceTranslationProvider
from .webrtc_vad import WebRTCVADProvider

logger = logging.getLogger(__name__)


class ProviderFactory:
    """Factory class for creating voice processing providers."""
    
    # Registry of available providers
    STT_PROVIDERS: Dict[str, Type[BaseSTTProvider]] = {
        "whisper": WhisperSTTProvider,
        "deepgram": DeepGramSTTProvider,
    }

    TTS_PROVIDERS: Dict[str, Type[BaseTTSProvider]] = {
        "openai": OpenAITTSProvider,
        "deepgram": DeepGramTTSProvider,
    }
    
    TRANSLATION_PROVIDERS: Dict[str, Type[BaseTranslationProvider]] = {
        "huggingface": HuggingFaceTranslationProvider,
    }
    
    VAD_PROVIDERS: Dict[str, Type[BaseVADProvider]] = {
        "webrtc": WebRTCVADProvider,
    }
    
    @classmethod
    def create_stt_provider(cls, provider_name: str, **kwargs) -> BaseSTTProvider:
        """
        Create an STT provider instance.
        
        Args:
            provider_name: Name of the provider to create
            **kwargs: Configuration parameters for the provider
            
        Returns:
            BaseSTTProvider: Configured STT provider instance
            
        Raises:
            ValueError: If provider_name is not supported
        """
        if provider_name not in cls.STT_PROVIDERS:
            available = ", ".join(cls.STT_PROVIDERS.keys())
            raise ValueError(f"Unknown STT provider '{provider_name}'. Available: {available}")
        
        provider_class = cls.STT_PROVIDERS[provider_name]
        logger.info(f"Creating STT provider: {provider_name}")
        return provider_class(**kwargs)
    
    @classmethod
    def create_tts_provider(cls, provider_name: str, **kwargs) -> BaseTTSProvider:
        """
        Create a TTS provider instance.
        
        Args:
            provider_name: Name of the provider to create
            **kwargs: Configuration parameters for the provider
            
        Returns:
            BaseTTSProvider: Configured TTS provider instance
            
        Raises:
            ValueError: If provider_name is not supported
        """
        if provider_name not in cls.TTS_PROVIDERS:
            available = ", ".join(cls.TTS_PROVIDERS.keys())
            raise ValueError(f"Unknown TTS provider '{provider_name}'. Available: {available}")
        
        provider_class = cls.TTS_PROVIDERS[provider_name]
        logger.info(f"Creating TTS provider: {provider_name}")
        return provider_class(**kwargs)
    
    @classmethod
    def create_translation_provider(cls, provider_name: str, **kwargs) -> BaseTranslationProvider:
        """
        Create a Translation provider instance.
        
        Args:
            provider_name: Name of the provider to create
            **kwargs: Configuration parameters for the provider
            
        Returns:
            BaseTranslationProvider: Configured Translation provider instance
            
        Raises:
            ValueError: If provider_name is not supported
        """
        if provider_name not in cls.TRANSLATION_PROVIDERS:
            available = ", ".join(cls.TRANSLATION_PROVIDERS.keys())
            raise ValueError(f"Unknown Translation provider '{provider_name}'. Available: {available}")
        
        provider_class = cls.TRANSLATION_PROVIDERS[provider_name]
        logger.info(f"Creating Translation provider: {provider_name}")
        return provider_class(**kwargs)
    
    @classmethod
    def create_vad_provider(cls, provider_name: str, **kwargs) -> BaseVADProvider:
        """
        Create a VAD provider instance.
        
        Args:
            provider_name: Name of the provider to create
            **kwargs: Configuration parameters for the provider
            
        Returns:
            BaseVADProvider: Configured VAD provider instance
            
        Raises:
            ValueError: If provider_name is not supported
        """
        if provider_name not in cls.VAD_PROVIDERS:
            available = ", ".join(cls.VAD_PROVIDERS.keys())
            raise ValueError(f"Unknown VAD provider '{provider_name}'. Available: {available}")
        
        provider_class = cls.VAD_PROVIDERS[provider_name]
        logger.info(f"Creating VAD provider: {provider_name}")
        return provider_class(**kwargs)
    
    @classmethod
    def register_stt_provider(cls, name: str, provider_class: Type[BaseSTTProvider]) -> None:
        """Register a new STT provider."""
        cls.STT_PROVIDERS[name] = provider_class
        logger.info(f"Registered STT provider: {name}")
    
    @classmethod
    def register_tts_provider(cls, name: str, provider_class: Type[BaseTTSProvider]) -> None:
        """Register a new TTS provider."""
        cls.TTS_PROVIDERS[name] = provider_class
        logger.info(f"Registered TTS provider: {name}")
    
    @classmethod
    def register_translation_provider(cls, name: str, provider_class: Type[BaseTranslationProvider]) -> None:
        """Register a new Translation provider."""
        cls.TRANSLATION_PROVIDERS[name] = provider_class
        logger.info(f"Registered Translation provider: {name}")
    
    @classmethod
    def register_vad_provider(cls, name: str, provider_class: Type[BaseVADProvider]) -> None:
        """Register a new VAD provider."""
        cls.VAD_PROVIDERS[name] = provider_class
        logger.info(f"Registered VAD provider: {name}")
    
    @classmethod
    def get_available_providers(cls) -> Dict[str, Any]:
        """Get list of all available providers."""
        return {
            "stt": list(cls.STT_PROVIDERS.keys()),
            "tts": list(cls.TTS_PROVIDERS.keys()),
            "translation": list(cls.TRANSLATION_PROVIDERS.keys()),
            "vad": list(cls.VAD_PROVIDERS.keys()),
        }
