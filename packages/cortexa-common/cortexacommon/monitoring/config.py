"""
Monitoring configuration for Cortexa services.

This module provides configuration classes for tracing, metrics, and observability
settings that can be used across all microservices.
"""

import os
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class TracingSettings(BaseSettings):
    """OpenTelemetry tracing configuration."""
    
    model_config = SettingsConfigDict(
        env_prefix="TRACING_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    enabled: bool = Field(default=False, description="Enable distributed tracing")
    service_name: str = Field(description="Service name for tracing")
    service_version: str = Field(default="0.1.0", description="Service version")
    service_instance_id: Optional[str] = Field(
        default=None, 
        description="Service instance ID (defaults to hostname)"
    )
    
    # Jaeger configuration
    jaeger_endpoint: Optional[str] = Field(
        default=None,
        description="Jaeger collector endpoint (e.g., localhost:14268)"
    )
    jaeger_agent_host: Optional[str] = Field(
        default=None,
        description="Jaeger agent host"
    )
    jaeger_agent_port: int = Field(
        default=14268,
        description="Jaeger agent port"
    )
    
    # OTLP configuration
    otlp_endpoint: Optional[str] = Field(
        default=None,
        description="OTLP endpoint (e.g., http://localhost:4317)"
    )
    otlp_headers: Optional[Dict[str, str]] = Field(
        default=None,
        description="OTLP headers for authentication"
    )
    
    # Sampling configuration
    sampling_ratio: float = Field(
        default=1.0,
        description="Trace sampling ratio (0.0 to 1.0)"
    )
    
    # Resource attributes
    resource_attributes: Dict[str, str] = Field(
        default_factory=dict,
        description="Additional resource attributes"
    )
    
    def get_service_instance_id(self) -> str:
        """Get service instance ID, defaulting to hostname."""
        return self.service_instance_id or os.getenv("HOSTNAME", "unknown")


class MetricsSettings(BaseSettings):
    """Prometheus metrics configuration."""
    
    model_config = SettingsConfigDict(
        env_prefix="METRICS_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    enabled: bool = Field(default=True, description="Enable metrics collection")
    endpoint: str = Field(default="/metrics", description="Metrics endpoint path")
    include_in_schema: bool = Field(
        default=False, 
        description="Include metrics endpoint in OpenAPI schema"
    )
    
    # FastAPI instrumentator settings
    should_group_status_codes: bool = Field(
        default=False,
        description="Group HTTP status codes in metrics"
    )
    should_ignore_untemplated: bool = Field(
        default=True,
        description="Ignore untemplated routes"
    )
    should_respect_env_var: bool = Field(
        default=True,
        description="Respect ENABLE_METRICS environment variable"
    )
    should_instrument_requests_inprogress: bool = Field(
        default=True,
        description="Track requests in progress"
    )
    
    # Excluded handlers
    excluded_handlers: List[str] = Field(
        default_factory=lambda: ["/metrics", "/health", "/docs", "/redoc", "/openapi.json"],
        description="Handlers to exclude from metrics"
    )
    
    # Custom metric labels
    custom_labels: Dict[str, str] = Field(
        default_factory=dict,
        description="Custom labels to add to all metrics"
    )


class HealthCheckSettings(BaseSettings):
    """Health check configuration."""
    
    model_config = SettingsConfigDict(
        env_prefix="HEALTH_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    enabled: bool = Field(default=True, description="Enable health checks")
    endpoint: str = Field(default="/health", description="Health check endpoint")
    readiness_endpoint: str = Field(default="/ready", description="Readiness check endpoint")
    liveness_endpoint: str = Field(default="/live", description="Liveness check endpoint")
    
    # Check intervals
    check_interval: int = Field(
        default=30,
        description="Health check interval in seconds"
    )
    
    # Timeout settings
    check_timeout: int = Field(
        default=5,
        description="Health check timeout in seconds"
    )
    
    # Dependencies to check
    check_database: bool = Field(default=True, description="Check database connectivity")
    check_redis: bool = Field(default=True, description="Check Redis connectivity")
    check_kafka: bool = Field(default=False, description="Check Kafka connectivity")
    check_external_services: bool = Field(
        default=False, 
        description="Check external service dependencies"
    )


class LoggingSettings(BaseSettings):
    """Logging configuration."""
    
    model_config = SettingsConfigDict(
        env_prefix="LOG_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    level: str = Field(default="INFO", description="Log level")
    format: str = Field(
        default="text",
        description="Log format (json, text)"
    )
    file: Optional[str] = Field(
        default=None,
        description="Log file path"
    )
    max_file_size: str = Field(
        default="100 MB",
        description="Maximum log file size"
    )
    retention: str = Field(
        default="30 days",
        description="Log retention period"
    )
    compression: str = Field(
        default="gz",
        description="Log compression format"
    )
    
    # Context settings
    include_trace: bool = Field(
        default=True,
        description="Include trace information in logs"
    )
    diagnose: bool = Field(
        default=False,
        description="Enable diagnostic information"
    )


class MonitoringSettings(BaseSettings):
    """Complete monitoring configuration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    service_name: str = Field(description="Service name")
    environment: str = Field(default="development", description="Environment name")
    
    # Component settings
    tracing: TracingSettings = Field(default_factory=TracingSettings)
    metrics: MetricsSettings = Field(default_factory=MetricsSettings)
    health: HealthCheckSettings = Field(default_factory=HealthCheckSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    
    def __init__(self, service_name: str, **kwargs):
        """Initialize monitoring settings with service name."""
        super().__init__(service_name=service_name, **kwargs)
        
        # Set service name in tracing settings
        if not hasattr(self.tracing, 'service_name') or not self.tracing.service_name:
            self.tracing.service_name = service_name


def get_monitoring_settings(service_name: str) -> MonitoringSettings:
    """
    Get monitoring settings for a service.
    
    Args:
        service_name: Name of the service
        
    Returns:
        MonitoringSettings instance
    """
    return MonitoringSettings(service_name=service_name)
