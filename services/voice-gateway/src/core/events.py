"""
Enhanced event publishing for voice-gateway.

This module provides structured event publishing to Kafka with
detailed transcription and processing metrics.
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, List
from datetime import datetime

from ..schemas.events import (
    BaseEvent, CallStartedEvent, CallEndedEvent, TranscriptionEvent,
    ProcessingMetricsEvent, ErrorEvent, ProviderSwitchEvent,
    QualityMetricsEvent, SystemHealthEvent
)

logger = logging.getLogger(__name__)


class EventPublisher:
    """Enhanced event publisher for voice-gateway events."""
    
    def __init__(self):
        """Initialize the event publisher."""
        self._producer = None
        self._initialized = False
        self._failed_events = []  # Store failed events for retry
    
    async def initialize(self):
        """Initialize Kafka producer."""
        try:
            # Import here to avoid circular imports and handle optional dependency
            from cortexacommon.events.producer import EventProducer
            from cortexacommon.config import KafkaSettings

            kafka_settings = KafkaSettings()
            self._producer = EventProducer(kafka_settings)
            await self._producer.start()
            self._initialized = True
            logger.info("Event publisher initialized")
            
            # Start background task to retry failed events
            asyncio.create_task(self._retry_failed_events())
            
        except ImportError:
            logger.warning("Kafka producer not available, events will be logged only")
            self._producer = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize event publisher: {e}")
            self._producer = None
            self._initialized = False

    async def cleanup(self):
        """Cleanup the event publisher."""
        if self._producer:
            try:
                await self._producer.stop()
                logger.info("Event publisher cleaned up")
            except Exception as e:
                logger.error(f"Error during event publisher cleanup: {e}")
            finally:
                self._producer = None
                self._initialized = False
    
    async def publish_event(self, event: BaseEvent, topic: Optional[str] = None) -> bool:
        """
        Publish an event to Kafka.
        
        Args:
            event: Event to publish
            topic: Kafka topic (defaults to event type)
            
        Returns:
            bool: True if published successfully
        """
        if not self._initialized or not self._producer:
            logger.debug(f"Event not published (no producer): {event.event_type}")
            return False
        
        try:
            # Use event type as topic if not specified
            if topic is None:
                topic = f"voice-gateway.{event.event_type.replace('.', '-')}"
            
            # Publish event
            await self._producer.publish_dict(topic, data=event.model_dump(), key=getattr(event, 'call_id', None))
            logger.debug(f"Published event {event.event_type} to topic {topic}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish event {event.event_type}: {e}")
            # Store failed event for retry
            self._failed_events.append((event, topic, time.time()))
            return False
    
    async def publish_call_started(self, call_id: str, user_id: str, providers: Dict[str, str],
                                 source_language: Optional[str] = None,
                                 target_language: Optional[str] = None) -> bool:
        """Publish call started event."""
        event = CallStartedEvent(
            call_id=call_id,
            user_id=user_id,
            source_language=source_language,
            target_language=target_language,
            providers=providers
        )
        return await self.publish_event(event)
    
    async def publish_call_ended(self, call_id: str, user_id: str, duration_seconds: float,
                               total_segments: int, total_audio_received: int,
                               total_audio_sent: int, error_count: int,
                               final_transcript: List[Dict[str, Any]]) -> bool:
        """Publish call ended event."""
        event = CallEndedEvent(
            call_id=call_id,
            user_id=user_id,
            duration_seconds=duration_seconds,
            total_segments=total_segments,
            total_audio_received_bytes=total_audio_received,
            total_audio_sent_bytes=total_audio_sent,
            error_count=error_count,
            final_transcript=final_transcript
        )
        return await self.publish_event(event)
    
    async def publish_transcription(self, call_id: str, segment_id: str,
                                  original_text: str, translated_text: str,
                                  confidence_score: float, processing_time_ms: int,
                                  audio_duration_ms: int, providers_used: Dict[str, str],
                                  language_detected: Optional[str] = None) -> bool:
        """Publish transcription completed event."""
        # Ensure confidence score is within valid range [0.0, 1.0]
        confidence_score = max(0.0, min(1.0, confidence_score))

        event = TranscriptionEvent(
            call_id=call_id,
            segment_id=segment_id,
            original_text=original_text,
            translated_text=translated_text,
            confidence_score=confidence_score,
            processing_time_ms=processing_time_ms,
            audio_duration_ms=audio_duration_ms,
            providers_used=providers_used,
            language_detected=language_detected
        )
        return await self.publish_event(event)
    
    async def publish_processing_metrics(self, call_id: str, segment_id: str,
                                       metrics: Dict[str, Any]) -> bool:
        """Publish processing metrics event."""
        event = ProcessingMetricsEvent(
            call_id=call_id,
            segment_id=segment_id,
            metrics=metrics
        )
        return await self.publish_event(event)
    
    async def publish_error(self, call_id: str, error_type: str, error_message: str,
                          component: str, segment_id: Optional[str] = None,
                          stack_trace: Optional[str] = None,
                          context: Optional[Dict[str, Any]] = None) -> bool:
        """Publish processing error event."""
        event = ErrorEvent(
            call_id=call_id,
            error_type=error_type,
            error_message=error_message,
            component=component,
            segment_id=segment_id,
            stack_trace=stack_trace,
            context=context or {}
        )
        return await self.publish_event(event)
    
    async def publish_provider_switch(self, call_id: str, provider_type: str,
                                    from_provider: str, to_provider: str,
                                    reason: str, failure_count: int) -> bool:
        """Publish provider switch event."""
        event = ProviderSwitchEvent(
            call_id=call_id,
            provider_type=provider_type,
            from_provider=from_provider,
            to_provider=to_provider,
            reason=reason,
            failure_count=failure_count
        )
        return await self.publish_event(event)
    
    async def publish_quality_metrics(self, call_id: str, window_start: datetime,
                                    window_end: datetime, metrics: Dict[str, Any]) -> bool:
        """Publish quality metrics event."""
        event = QualityMetricsEvent(
            call_id=call_id,
            window_start=window_start,
            window_end=window_end,
            metrics=metrics
        )
        return await self.publish_event(event)
    
    async def publish_system_health(self, active_connections: int, total_calls_today: int,
                                  provider_status: Dict[str, bool],
                                  system_resources: Dict[str, Any],
                                  performance_metrics: Dict[str, float]) -> bool:
        """Publish system health event."""
        event = SystemHealthEvent(
            active_connections=active_connections,
            total_calls_today=total_calls_today,
            provider_status=provider_status,
            system_resources=system_resources,
            performance_metrics=performance_metrics
        )
        return await self.publish_event(event)
    
    async def _retry_failed_events(self):
        """Background task to retry failed events."""
        while True:
            try:
                await asyncio.sleep(30)  # Retry every 30 seconds
                
                if not self._failed_events:
                    continue
                
                # Retry events older than 5 seconds
                current_time = time.time()
                events_to_retry = []
                remaining_events = []
                
                for event, topic, timestamp in self._failed_events:
                    if current_time - timestamp > 5:
                        events_to_retry.append((event, topic))
                    else:
                        remaining_events.append((event, topic, timestamp))
                
                self._failed_events = remaining_events
                
                # Retry events
                for event, topic in events_to_retry:
                    success = await self.publish_event(event, topic)
                    if success:
                        logger.info(f"Successfully retried event {event.event_type}")
                    else:
                        # Add back to failed events with new timestamp
                        self._failed_events.append((event, topic, current_time))
                
            except Exception as e:
                logger.error(f"Error in retry failed events task: {e}")


# Global event publisher instance
_event_publisher: Optional[EventPublisher] = None


async def get_event_publisher() -> EventPublisher:
    """Get the global event publisher instance."""
    global _event_publisher
    if _event_publisher is None:
        _event_publisher = EventPublisher()
        await _event_publisher.initialize()
    return _event_publisher
